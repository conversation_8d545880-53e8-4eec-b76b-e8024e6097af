/**
 * 淘宝产品数据管理 Node.js Express 路由
 */

import express from 'express';
import path from 'path';
import { config } from './config.js';
import { database } from './database.js';
import ProductCollection from './product-collection.js';
import { syncSingleAnchor, handleUpdateCookie } from './job.js';
import { batchUpdateAllAnchorsCookies } from './cookie-update-service.js';
import cookieScheduler from './cookie-scheduler.js';


// 创建路由器
const router = express.Router();

// 常量定义
const CONSTANTS = {
  AUTH: {
    SPECIAL_APIS: ['anchors', 'products']
  },
  PAGINATION: {
    DEFAULT_PAGE: 1,
    DEFAULT_LIMIT: 10,
    DEFAULT_SORT_FIELD: 'created_at',
    DEFAULT_SORT_ORDER: 'DESC'
  }
};

// 中间件：验证API密钥
async function authMiddleware(req, res, next) {
  try {
    const authResult = await validateApiKey(req);
    if (!authResult.valid) {
      return res.status(401).json({
        error: "invalid_api_key",
        message: "无效的访问密码"
      });
    }
    req.authResult = authResult;
    next();
  } catch (error) {
    console.error('认证中间件错误:', error);
    res.status(500).json({
      error: "auth_error",
      message: "认证失败"
    });
  }
}

// API路由定义

router.get("/api/anchors", authMiddleware, handleAnchorNames);
router.post("/api/anchors", authMiddleware, handleAddAnchor);
router.get("/api/anchors/list", authMiddleware, handleAnchorsData);
router.get("/api/anchors/stats", authMiddleware, handleAnchorsStats);
router.post("/api/anchors/check-password", authMiddleware, handleCheckPassword);
router.get("/api/products", authMiddleware, handleProductsData);
router.get("/api/products/stats", authMiddleware, handleProductsStats);
router.get("/api/products/export", authMiddleware, handleProductsExport);
router.post("/api/products/batch", authMiddleware, handleProductsBatch);

// 选品规则管理
router.get("/api/selection-rules", authMiddleware, handleGetSelectionRules);
router.post("/api/selection-rules", authMiddleware, handleCreateSelectionRule);
router.put("/api/selection-rules/:ruleId", authMiddleware, handleUpdateSelectionRule);
router.get("/api/selection-rules/history", authMiddleware, handleGetExecutionHistory);
router.get("/api/selection-rules/history/:executionId", authMiddleware, handleGetExecutionDetail);
router.get("/api/selection-rules/:ruleId", authMiddleware, handleGetSelectionRule);
router.delete("/api/selection-rules/:ruleId", authMiddleware, handleDeleteSelectionRule);
router.post("/api/selection-rules/preview", authMiddleware, handlePreviewSelectionRule);
router.post("/api/selection-rules/preview-all", authMiddleware, handlePreviewAllSelectionRules);
router.post("/api/selection-rules/execute", authMiddleware, handleExecuteSelectionRule);

// 商品采集页面路由
router.get("/product-collection", (req, res) => {
    res.sendFile(path.join(__dirname, '../public/product-collection.html'));
});

// 商品采集相关路由
router.get("/api/product-collection/anchors", authMiddleware, handleGetCollectionAnchors);
router.post("/api/product-collection/collect", authMiddleware, handleCollectProducts);

// 动态路由

router.get("/api/anchors/:anchorId", authMiddleware, (req, res) => {
  handleGetAnchor(req.params.anchorId, req, res);
});

router.put("/api/anchors/:anchorId", authMiddleware, (req, res) => {
  handleUpdateAnchor(req.params.anchorId, req, res);
});

router.delete("/api/anchors/:anchorId", authMiddleware, (req, res) => {
  handleDeleteAnchor(req.params.anchorId, req, res);
});

// 同步接口（不需要认证）
router.post("/sync-anchor", async (req, res) => {
  try {
    const result = await syncSingleAnchor(req.body);
    res.json(result);
  } catch (error) {
    console.error('同步失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Cookie更新接口（不需要认证）
router.post("/api/update-cookie", async (req, res) => {
  await handleUpdateCookie(req, res);
});

// 批量更新主播Cookie接口
router.post("/api/anchors/update-cookies", authMiddleware, async (req, res) => {
  await handleBatchUpdateCookies(req, res);
});

// 获取Cookie更新状态接口
router.get("/api/anchors/cookie-status", authMiddleware, async (req, res) => {
  await handleGetCookieStatus(req, res);
});

/**
 * 公共工具函数
 */

// 解析请求的cookies
function parseCookies(request) {
  const cookieString = request.headers.cookie || '';
  return cookieString.split(';').reduce((cookies, cookie) => {
    const [name, value] = cookie.trim().split('=');
    if (name && value) {
      cookies[name] = decodeURIComponent(value);
    }
    return cookies;
  }, {});
}

// 创建错误响应 - Express版本
function createErrorResponse(error, message, status = 500) {
  return { error, message, status };
}



// 格式化日期
function formatDate(dateStr) {
  if (!dateStr) return "";

  try {
    const date = new Date(dateStr);
    return date.getFullYear() +
          '-' + String(date.getMonth() + 1).padStart(2, '0') +
          '-' + String(date.getDate()).padStart(2, '0') +
          ' ' + String(date.getHours()).padStart(2, '0') +
          ':' + String(date.getMinutes()).padStart(2, '0') +
          ':' + String(date.getSeconds()).padStart(2, '0');
  } catch (e) {
    return dateStr;
  }
}

// 验证访问密码 - Express版本
async function validateApiKey(request) {
  const apiKey = request.headers['x-api-key'];
  const cookies = parseCookies(request);
  const inputPassword = apiKey || cookies['api_key'];

  if (!inputPassword) {
    return {
      valid: false,
      response: createErrorResponse("invalid_api_key", "无效的访问密码", 401)
    };
  }

  // 检查是否为管理员密码
  if (inputPassword === config.auth.password) {
    return {
      valid: true,
      userType: 'admin',
      anchorInfo: null
    };
  }

  // 检查是否为主播密码
  try {
    const result = await database.all(
      "SELECT id, anchor_name, anchor_id FROM anchors WHERE password = ? AND status = 'active'",
      [inputPassword]
    );

    if (result.results?.length > 0) {
      const anchor = result.results[0];
      return {
        valid: true,
        userType: 'anchor',
        anchorInfo: {
          id: anchor.id,
          anchor_name: anchor.anchor_name,
          anchor_id: anchor.anchor_id
        }
      };
    }
  } catch (error) {
    console.error("Error checking anchor password:", error);
  }

  return {
    valid: false,
    response: createErrorResponse("invalid_api_key", "无效的访问密码", 401)
  };
}

// 公共查询和分页处理函数

// 获取分页参数 - Express版本
function getPaginationParams(req) {
  return {
    page: parseInt(req.query.page || CONSTANTS.PAGINATION.DEFAULT_PAGE),
    limit: parseInt(req.query.limit || CONSTANTS.PAGINATION.DEFAULT_LIMIT),
    sortField: req.query.sortField || CONSTANTS.PAGINATION.DEFAULT_SORT_FIELD,
    sortOrder: req.query.sortOrder || CONSTANTS.PAGINATION.DEFAULT_SORT_ORDER
  };
}

// 安全的排序处理
function getSafeSorting(sortField, sortOrder, validFields) {
  const actualSortField = validFields.includes(sortField) ? sortField : validFields[0];
  const actualSortOrder = ['ASC', 'DESC'].includes(sortOrder?.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';
  return { actualSortField, actualSortOrder };
}

// 应用主播权限过滤 - 根据表类型使用不同的字段
function applyAnchorFilter(filters, authResult, tableType = 'anchors') {
  if (authResult.userType === 'anchor' && authResult.anchorInfo) {
    // 根据不同表类型设置相应的字段
    switch (tableType) {
      case 'anchors':
        // 主播表使用 anchor_id 字段
        filters.anchorId = authResult.anchorInfo.anchor_id;
        break;
      default:
        console.warn(`Unknown table type: ${tableType}`);
    }
  }
  return filters;
}

// 构建查询条件和参数
function buildQueryConditions(filters) {
  const conditions = [];
  const params = [];

  const conditionMap = {
    anchorName: { sql: "anchor_name = ?", value: (v) => v },
    anchorId: { sql: "anchor_id = ?", value: (v) => v },
    status: { sql: "status = ?", value: (v) => v }
  };

  Object.entries(filters).forEach(([key, value]) => {
    if (value && conditionMap[key]) {
      conditions.push(conditionMap[key].sql);
      params.push(conditionMap[key].value(value));
    }
  });

  return { conditions, params };
}

// 格式化数据通用函数
function formatDataResults(results, dateFields = ['created_at', 'updated_at'], numberFields = []) {
  return results.map(item => {
    const formatted = { ...item };

    // 格式化日期字段
    dateFields.forEach(field => {
      if (formatted[field]) {
        formatted[field] = formatDate(formatted[field]);
      }
    });

    // 格式化数字字段
    numberFields.forEach(field => {
      if (formatted[field] !== undefined) {
        formatted[field] = Number(formatted[field] || 0);
      }
    });

    return formatted;
  });
}

// 密码验证公共函数
async function validatePassword(password, excludeId) {
  if (!password) {
    return {
      isValid: false,
      message: "密码不能为空"
    };
  }

  // 检查是否与系统访问密码相同
  if (password === config.auth.password) {
    return {
      isValid: false,
      message: "密码不能与系统访问密码相同，请使用其他密码"
    };
  }

  let sql = "SELECT anchor_name FROM anchors WHERE password = ?";
  let params = [password];

  if (excludeId) {
    sql += " AND id != ?";
    params.push(excludeId);
  }

  const result = await database.all(sql, params);

  if (result.results.length > 0) {
    return {
      isValid: false,
      message: `密码已被主播"${result.results[0].anchor_name}"使用，请使用其他密码`
    };
  }

  return {
    isValid: true,
    message: "密码可以使用"
  };
}

// 权限检查公共函数
function checkAnchorPermission(authResult, operation = 'modify') {
  if (authResult.userType === 'anchor') {
    const operations = {
      modify: "主播用户无权修改主播信息",
      delete: "主播用户无权删除主播信息",
      add: "主播用户无权添加新主播",
      check: "主播用户无权检查密码"
    };

    return {
      hasPermission: false,
      message: operations[operation] || "主播用户无权执行此操作"
    };
  }

  return {
    hasPermission: true,
    message: ""
  };
}











/**
 * 获取主播列表 - 支持两种模式：名称列表和完整信息 - Express版本
 */
async function handleAnchorNames(req, res) {
  try {
    const mode = req.query.mode || "names";

    if (mode === "full") {
      // 返回完整的主播信息（用于同步弹窗）
      let sql = "SELECT id, anchor_name, anchor_id, sort, status, created_at, updated_at FROM anchors";
      let params = [];
      let whereConditions = [];

      // 如果是主播用户，只返回自己的信息
      if (req.authResult.userType === 'anchor' && req.authResult.anchorInfo) {
        whereConditions.push("id = ?");
        params.push(req.authResult.anchorInfo.id);
      }

      // 添加WHERE子句
      if (whereConditions.length > 0) {
        sql += " WHERE " + whereConditions.join(" AND ");
      }

      sql += " ORDER BY sort ASC, anchor_name ASC";
      const result = await database.all(sql, params);

      const anchors = result.results?.map(anchor => ({
        ...anchor,
        created_at: formatDate(anchor.created_at),
        updated_at: formatDate(anchor.updated_at)
      })) || [];

      res.json({
        anchors,
        userInfo: {
          userType: req.authResult.userType,
          anchorInfo: req.authResult.anchorInfo
        }
      });
    } else {
      // 返回主播名称列表（用于筛选下拉框）
      let sql = "SELECT DISTINCT ad_user_nick FROM orders WHERE ad_user_nick IS NOT NULL AND ad_user_nick != ''";
      let params = [];

      // 如果是主播用户，只返回自己的名称
      if (req.authResult.userType === 'anchor' && req.authResult.anchorInfo) {
        sql += " AND ad_user_nick = ?";
        params.push(req.authResult.anchorInfo.anchor_name);
      }

      sql += " ORDER BY ad_user_nick";
      const result = await database.all(sql, params);
      const anchorNames = result.results?.map(item => item.ad_user_nick) || [];

      res.json({
        anchorNames,
        userInfo: {
          userType: req.authResult.userType,
          anchorInfo: req.authResult.anchorInfo
        }
      });
    }
  } catch (error) {
    console.error("Error fetching anchor data:", error);
    res.status(500).json({
      error: "Failed to fetch anchor data",
      message: error.message
    });
  }
}





/**
 * 获取主播列表数据 - Express版本
 */
async function handleAnchorsData(req, res) {
  try {
    const { page, limit } = getPaginationParams(req);

    let filters = {
      anchorName: req.query.anchorName,
      anchorId: req.query.anchorId,
      status: req.query.status
    };

    filters = applyAnchorFilter(filters, req.authResult, 'anchors');
    const { conditions, params } = buildQueryConditions(filters);

    let sql = "SELECT id, anchor_name, anchor_id, sort, status, created_at, updated_at FROM anchors";
    let countSql = "SELECT COUNT(*) as total FROM anchors";

    if (conditions.length > 0) {
      const whereClause = " WHERE " + conditions.join(" AND ");
      sql += whereClause;
      countSql += whereClause;
    }

    sql += " ORDER BY sort ASC, created_at DESC LIMIT ? OFFSET ?";
    const offset = (page - 1) * limit;

    const [countResult, dataResult] = await Promise.all([
      database.all(countSql, params),
      database.all(sql, [...params, limit, offset])
    ]);

    const total = countResult.results[0]?.total || 0;
    const anchors = formatDataResults(dataResult.results);

    res.json({
      anchors,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      },
      userInfo: {
        userType: req.authResult.userType,
        anchorInfo: req.authResult.anchorInfo
      }
    });
  } catch (error) {
    console.error("Error fetching anchors:", error);
    res.status(500).json({
      error: "Failed to fetch anchors",
      message: error.message
    });
  }
}

/**
 * 获取主播统计数据 - Express版本
 */
async function handleAnchorsStats(req, res) {
  try {
    let filters = {
      anchorName: req.query.anchorName,
      anchorId: req.query.anchorId,
      status: req.query.status
    };

    filters = applyAnchorFilter(filters, req.authResult, 'anchors');
    const { conditions, params } = buildQueryConditions(filters);
    const whereClause = conditions.length > 0 ? " WHERE " + conditions.join(" AND ") : "";

    const statsSql = `
      SELECT
        COUNT(*) as totalAnchors,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as activeAnchors
      FROM anchors
      ${whereClause}
    `;

    const statsResult = await database.all(statsSql, params);
    const stats = statsResult.results[0];

    res.json({
      totalAnchors: stats.totalAnchors || 0,
      activeAnchors: stats.activeAnchors || 0,
      userInfo: {
        userType: req.authResult.userType,
        anchorInfo: req.authResult.anchorInfo
      }
    });
  } catch (error) {
    console.error("Error fetching anchors stats:", error);
    res.status(500).json({
      error: "Failed to fetch anchors statistics",
      message: error.message
    });
  }
}

/**
 * 添加主播 - Express版本
 */
async function handleAddAnchor(req, res) {
  try {
    const permissionCheck = checkAnchorPermission(req.authResult, 'add');
    if (!permissionCheck.hasPermission) {
      return res.status(403).json({
        error: "permission_denied",
        message: permissionCheck.message
      });
    }

    const { anchor_name, anchor_id, anchor_cookie, status, password, sort } = req.body;

    // 验证必填字段
    if (!anchor_name || !anchor_id || !password || !anchor_cookie) {
      return res.status(400).json({
        error: "missing_fields",
        message: "主播名称、主播ID、密码和主播Cookie为必填字段"
      });
    }

    // 验证密码
    const passwordValidation = await validatePassword(password, null);
    if (!passwordValidation.isValid) {
      return res.status(400).json({
        error: "invalid_password",
        message: passwordValidation.message
      });
    }

    // 检查主播ID是否已存在
    const checkResult = await database.all(
      "SELECT anchor_id FROM anchors WHERE anchor_id = ?",
      [anchor_id]
    );

    if (checkResult.results.length > 0) {
      return res.status(400).json({
        error: "duplicate_anchor_id",
        message: "主播ID已存在"
      });
    }

    // 插入新主播
    const insertResult = await database.run(`
      INSERT INTO anchors (anchor_name, anchor_id, anchor_cookie, status, password, sort, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `, [anchor_name, anchor_id, anchor_cookie, status || 'active', password, sort || 0]);

    res.json({
      success: true,
      message: "主播添加成功",
      id: insertResult.meta.last_row_id
    });
  } catch (error) {
    console.error("Error adding anchor:", error);
    res.status(500).json({
      error: "add_anchor_failed",
      message: "添加主播失败: " + error.message
    });
  }
}

/**
 * 获取单个主播 - Express版本
 */
async function handleGetAnchor(anchorId, req, res) {
  try {
    if (!anchorId) {
      return res.status(400).json({ error: "无效的主播ID" });
    }

    // 如果是主播用户，只能查看自己的信息
    if (req.authResult.userType === 'anchor' && req.authResult.anchorInfo) {
      if (parseInt(anchorId) !== req.authResult.anchorInfo.id) {
        return res.status(403).json({ error: "无权访问其他主播信息" });
      }
    }

    // 查询主播信息（包含密码字段）
    const result = await database.all(
      "SELECT id, anchor_name, anchor_id, anchor_cookie, status, password, sort, total_orders, total_amount, created_at, updated_at FROM anchors WHERE id = ?",
      [anchorId]
    );

    if (result.results.length === 0) {
      return res.status(404).json({ error: "主播不存在" });
    }

    const anchor = result.results[0];

    // 格式化数据
    const formattedAnchor = {
      ...anchor,
      created_at: formatDate(anchor.created_at),
      updated_at: formatDate(anchor.updated_at)
    };

    res.json({
      anchor: formattedAnchor
    });
  } catch (error) {
    console.error("Error fetching anchor:", error);
    res.status(500).json({
      error: "获取主播信息失败",
      details: error.message
    });
  }
}

/**
 * 更新主播 - Express版本
 */
async function handleUpdateAnchor(anchorId, req, res) {
  try {
    if (!anchorId) {
      return res.status(400).json({
        error: "invalid_anchor_id",
        message: "无效的主播ID"
      });
    }

    const permissionCheck = checkAnchorPermission(req.authResult, 'modify');
    if (!permissionCheck.hasPermission) {
      return res.status(403).json({
        error: "permission_denied",
        message: permissionCheck.message
      });
    }

    const { anchor_name, anchor_id, anchor_cookie, status, password, sort } = req.body;

    // 验证必填字段
    if (!anchor_name || !anchor_id || !password || !anchor_cookie) {
      return res.status(400).json({
        error: "missing_fields",
        message: "主播名称、主播ID、密码和主播Cookie为必填字段"
      });
    }

    // 验证密码
    const passwordValidation = await validatePassword(password, anchorId);
    if (!passwordValidation.isValid) {
      return res.status(400).json({
        error: "invalid_password",
        message: passwordValidation.message
      });
    }

    // 检查主播是否存在
    const checkResult = await database.all(
      "SELECT id FROM anchors WHERE id = ?",
      [anchorId]
    );

    if (checkResult.results.length === 0) {
      return res.status(404).json({
        error: "anchor_not_found",
        message: "主播不存在"
      });
    }

    // 检查主播ID是否被其他主播使用
    const duplicateResult = await database.all(
      "SELECT id FROM anchors WHERE anchor_id = ? AND id != ?",
      [anchor_id, anchorId]
    );

    if (duplicateResult.results.length > 0) {
      return res.status(400).json({
        error: "duplicate_anchor_id",
        message: "主播ID已被其他主播使用"
      });
    }

    // 更新主播信息
    await database.run(`
      UPDATE anchors
      SET anchor_name = ?, anchor_id = ?, anchor_cookie = ?, status = ?, password = ?, sort = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [anchor_name, anchor_id, anchor_cookie, status || 'active', password, sort || 0, anchorId]);

    res.json({
      success: true,
      message: "主播更新成功"
    });
  } catch (error) {
    console.error("Error updating anchor:", error);
    res.status(500).json({
      error: "update_anchor_failed",
      message: "更新主播失败: " + error.message
    });
  }
}

/**
 * 删除主播 - Express版本
 */
async function handleDeleteAnchor(anchorId, req, res) {
  try {
    if (!anchorId) {
      return res.status(400).json({
        error: "invalid_anchor_id",
        message: "无效的主播ID"
      });
    }

    const permissionCheck = checkAnchorPermission(req.authResult, 'delete');
    if (!permissionCheck.hasPermission) {
      return res.status(403).json({
        error: "permission_denied",
        message: permissionCheck.message
      });
    }

    // 检查主播是否存在
    const checkResult = await database.all(
      "SELECT id FROM anchors WHERE id = ?",
      [anchorId]
    );

    if (checkResult.results.length === 0) {
      return res.status(404).json({
        error: "anchor_not_found",
        message: "主播不存在"
      });
    }

    // 删除主播
    await database.run(
      "DELETE FROM anchors WHERE id = ?",
      [anchorId]
    );

    res.json({
      success: true,
      message: "主播删除成功"
    });
  } catch (error) {
    console.error("Error deleting anchor:", error);
    res.status(500).json({
      error: "delete_anchor_failed",
      message: "删除主播失败: " + error.message
    });
  }
}





/**
 * 检查密码是否重复 - Express版本
 */
async function handleCheckPassword(req, res) {
  try {
    const permissionCheck = checkAnchorPermission(req.authResult, 'check');
    if (!permissionCheck.hasPermission) {
      return res.status(403).json({
        error: "permission_denied",
        message: permissionCheck.message
      });
    }

    const { password, excludeId } = req.body;

    if (!password) {
      return res.status(400).json({
        error: "missing_password",
        message: "密码为必填参数"
      });
    }

    const validation = await validatePassword(password, excludeId);

    res.json({
      isDuplicate: !validation.isValid,
      isValid: validation.isValid,
      message: validation.message,
      usedBy: validation.isValid ? null : (password === config.auth.password ? "系统访问密码" : "其他主播")
    });
  } catch (error) {
    console.error("Error checking password:", error);
    res.status(500).json({
      error: "check_password_failed",
      message: "检查密码失败: " + error.message
    });
  }
}





/**
 * 产品数据处理函数
 */

// 处理产品数据请求
async function handleProductsData(req, res) {
  try {
    const {
      anchor,
      minPrice,
      maxPrice,
      minCommission,
      minSales365,
      maxSales365,
      minGrowthRate7Days,
      productSources,
      allianceChannels,
      tags,
      productTypes,
      batchNumber,
      startDate,
      endDate,
      date,
      page = CONSTANTS.PAGINATION.DEFAULT_PAGE,
      limit = CONSTANTS.PAGINATION.DEFAULT_LIMIT,
      sortField = 'created_at',
      sortOrder = CONSTANTS.PAGINATION.DEFAULT_SORT_ORDER
    } = req.query;

    // 构建WHERE条件
    let whereConditions = [];
    let params = [];

    // 主播筛选
    if (anchor && anchor.trim() !== '') {
      whereConditions.push('anchor_name = ?');
      params.push(anchor.trim());
    }

    // 价格区间筛选
    if (minPrice && !isNaN(minPrice)) {
      whereConditions.push('product_price >= ?');
      params.push(parseFloat(minPrice));
    }

    if (maxPrice && !isNaN(maxPrice)) {
      whereConditions.push('product_price < ?');
      params.push(parseFloat(maxPrice));
    }

    // 佣金筛选 - 大于
    if (minCommission && !isNaN(minCommission)) {
      whereConditions.push('commission_amount > ?');
      params.push(parseFloat(minCommission));
    }

    // 365天销量区间筛选
    if (minSales365 && !isNaN(minSales365)) {
      whereConditions.push('sales_365_days >= ?');
      params.push(parseInt(minSales365));
    }

    if (maxSales365 && !isNaN(maxSales365)) {
      whereConditions.push('sales_365_days <= ?');
      params.push(parseInt(maxSales365));
    }

    // 7天增长率筛选 - 大于
    if (minGrowthRate7Days && !isNaN(minGrowthRate7Days)) {
      whereConditions.push('sales_7_days_growth_rate > ?');
      params.push(parseFloat(minGrowthRate7Days));
    }

    // 产品来源多选筛选
    if (productSources && productSources.trim() !== '') {
      const sources = productSources.split(',').map(s => s.trim()).filter(s => s);
      if (sources.length > 0) {
        const placeholders = sources.map(() => '?').join(',');
        whereConditions.push(`product_source IN (${placeholders})`);
        params.push(...sources);
      }
    }

    // 联盟商品渠道多选筛选
    if (allianceChannels && allianceChannels.trim() !== '') {
      const channels = allianceChannels.split(',').map(c => c.trim()).filter(c => c);
      if (channels.length > 0) {
        const placeholders = channels.map(() => '?').join(',');
        whereConditions.push(`alliance_channel IN (${placeholders})`);
        params.push(...channels);
      }
    }

    // 标签多选筛选
    if (tags && tags.trim() !== '') {
      const tagList = tags.split(',').map(t => t.trim()).filter(t => t && !isNaN(t));
      if (tagList.length > 0) {
        const placeholders = tagList.map(() => '?').join(',');
        whereConditions.push(`tag IN (${placeholders})`);
        params.push(...tagList.map(t => parseInt(t)));
      }
    }

    // 产品类型多选筛选
    if (productTypes && productTypes.trim() !== '') {
      const typeList = productTypes.split(',').map(t => t.trim()).filter(t => t);
      if (typeList.length > 0) {
        const placeholders = typeList.map(() => '?').join(',');
        whereConditions.push(`product_type IN (${placeholders})`);
        params.push(...typeList);
      }
    }

    // 批次号筛选
    if (batchNumber && batchNumber.trim() !== '') {
      whereConditions.push('batch_number LIKE ?');
      params.push(`%${batchNumber.trim()}%`);
    }

    // 时间范围筛选
    if (startDate && startDate.trim() !== '') {
      whereConditions.push('DATE(created_at) >= ?');
      params.push(startDate.trim());
    }
 // 时间范围筛选
    if (date && date.trim() !== '') {
      whereConditions.push('date= ?');
      params.push(date.trim());
    }
    if (endDate && endDate.trim() !== '') {
      whereConditions.push('DATE(created_at) <= ?');
      params.push(endDate.trim());
    }

    // 用户权限筛选
    if (req.authResult.userType === 'anchor' && req.authResult.anchorInfo) {
      whereConditions.push('anchor_name = ?');
      params.push(req.authResult.anchorInfo.anchor_name);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 验证排序字段
    const validSortFields = ['product_id', 'product_price', 'commission_rate', 'commission_amount', 'sales_30_days', 'sales_7_days', 'sales_7_days_growth_rate', 'anchor_sales_30_days', 'shop_name', 'safe_shop_id', 'tag', 'product_type', 'date', 'created_at'];
    const finalSortField = validSortFields.includes(sortField) ? sortField : 'created_at';
    const finalSortOrder = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    // 计算分页
    const pageNum = Math.max(1, parseInt(page) || 1);
    const pageSize = Math.min(100, Math.max(1, parseInt(limit) || 10));
    const offset = (pageNum - 1) * pageSize;

    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM products ${whereClause}`;
    const countResult = await database.prepare(countQuery).get(params);
    const totalItems = countResult.total;
    const totalPages = Math.ceil(totalItems / pageSize);

    // 获取产品数据
    const dataQuery = `
      SELECT * FROM products
      ${whereClause}
      ORDER BY ${finalSortField} ${finalSortOrder}
      LIMIT ? OFFSET ?
    `;
  
    const dataParams = [...params, pageSize, offset];
    const productsResult = await database.prepare(dataQuery).all(dataParams);
    const products = productsResult.results || [];

    res.json({
      products: products,
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalItems,
        pageSize,
        hasNext: pageNum < totalPages,
        hasPrev: pageNum > 1
      },
      userInfo: {
        userType: req.authResult.userType,
        anchorInfo: req.authResult.anchorInfo
      }
    });

  } catch (error) {
    console.error("Error fetching products:", error);
    res.status(500).json({
      error: "Failed to fetch products",
      message: error.message
    });
  }
}

// 处理产品统计数据请求
async function handleProductsStats(req, res) {
  try {
    const {
      anchor,
      minPrice,
      maxPrice,
      minCommission,
      minSales365,
      maxSales365,
      minGrowthRate7Days,
      productSources,
      allianceChannels,
      tags,
      productTypes,
      batchNumber,
      startDate,
      endDate,
      date
    } = req.query;

    // 构建WHERE条件（与产品数据查询相同的逻辑）
    let whereConditions = [];
    let params = [];

    if (anchor && anchor.trim() !== '') {
      whereConditions.push('anchor_name = ?');
      params.push(anchor.trim());
    }

    if (minPrice && !isNaN(minPrice)) {
      whereConditions.push('product_price >= ?');
      params.push(parseFloat(minPrice));
    }

    if (maxPrice && !isNaN(maxPrice)) {
      whereConditions.push('product_price < ?');
      params.push(parseFloat(maxPrice));
    }

    // 佣金筛选 - 大于
    if (minCommission && !isNaN(minCommission)) {
      whereConditions.push('commission_amount > ?');
      params.push(parseFloat(minCommission));
    }

    // 365天销量区间筛选
    if (minSales365 && !isNaN(minSales365)) {
      whereConditions.push('sales_365_days >= ?');
      params.push(parseInt(minSales365));
    }

    if (maxSales365 && !isNaN(maxSales365)) {
      whereConditions.push('sales_365_days <= ?');
      params.push(parseInt(maxSales365));
    }

    // 7天增长率筛选 - 大于
    if (minGrowthRate7Days && !isNaN(minGrowthRate7Days)) {
      whereConditions.push('sales_7_days_growth_rate > ?');
      params.push(parseFloat(minGrowthRate7Days));
    }

    if (productSources && productSources.trim() !== '') {
      const sources = productSources.split(',').map(s => s.trim()).filter(s => s);
      if (sources.length > 0) {
        const placeholders = sources.map(() => '?').join(',');
        whereConditions.push(`product_source IN (${placeholders})`);
        params.push(...sources);
      }
    }

    if (allianceChannels && allianceChannels.trim() !== '') {
      const channels = allianceChannels.split(',').map(c => c.trim()).filter(c => c);
      if (channels.length > 0) {
        const placeholders = channels.map(() => '?').join(',');
        whereConditions.push(`alliance_channel IN (${placeholders})`);
        params.push(...channels);
      }
    }

    // 标签多选筛选
    if (tags && tags.trim() !== '') {
      const tagList = tags.split(',').map(t => t.trim()).filter(t => t && !isNaN(t));
      if (tagList.length > 0) {
        const placeholders = tagList.map(() => '?').join(',');
        whereConditions.push(`tag IN (${placeholders})`);
        params.push(...tagList.map(t => parseInt(t)));
      }
    }

    // 产品类型多选筛选
    if (productTypes && productTypes.trim() !== '') {
      const typeList = productTypes.split(',').map(t => t.trim()).filter(t => t);
      if (typeList.length > 0) {
        const placeholders = typeList.map(() => '?').join(',');
        whereConditions.push(`product_type IN (${placeholders})`);
        params.push(...typeList);
      }
    }

    if (batchNumber && batchNumber.trim() !== '') {
      whereConditions.push('batch_number LIKE ?');
      params.push(`%${batchNumber.trim()}%`);
    }

    if (startDate && startDate.trim() !== '') {
      whereConditions.push('DATE(created_at) >= ?');
      params.push(startDate.trim());
    }

    // 报表日期筛选
    if (date && date.trim() !== '') {
      whereConditions.push('date = ?');
      params.push(date.trim());
    }

    if (endDate && endDate.trim() !== '') {
      whereConditions.push('DATE(created_at) <= ?');
      params.push(endDate.trim());
    }

    // 用户权限筛选
    if (req.authResult.userType === 'anchor' && req.authResult.anchorInfo) {
      whereConditions.push('anchor_name = ?');
      params.push(req.authResult.anchorInfo.anchor_name);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 获取统计数据
    const statsQuery = `
      SELECT
        COUNT(*) as totalProducts,
        AVG(product_price) as avgPrice,
        AVG(commission_amount) as avgCommission,
        SUM(sales_30_days) as totalSales30,
        COUNT(DISTINCT anchor_name) as totalAnchors
      FROM products
      ${whereClause}
    `;

    const stats = await database.prepare(statsQuery).get(params);

    res.json({
      totalProducts: stats.totalProducts || 0,
      avgPrice: Number(stats.avgPrice) || 0,
      avgCommission: Number(stats.avgCommission) || 0,
      totalSales30: stats.totalSales30 || 0,
      totalAnchors: stats.totalAnchors || 0
    });

  } catch (error) {
    console.error("Error fetching products stats:", error);
    res.status(500).json({
      error: "Failed to fetch products stats",
      message: error.message
    });
  }
}

// 处理产品数据导出请求
async function handleProductsExport(req, res) {
  try {
    const {
      anchor,
      minPrice,
      maxPrice,
      minCommission,
      minSales365,
      maxSales365,
      minGrowthRate7Days,
      productSources,
      allianceChannels,
      tags,
      productTypes,
      batchNumber,
      startDate,
      endDate,
      date
    } = req.query;

    // 构建WHERE条件（与产品数据查询相同的逻辑）
    let whereConditions = [];
    let params = [];

    if (anchor && anchor.trim() !== '') {
      whereConditions.push('anchor_name = ?');
      params.push(anchor.trim());
    }

    if (minPrice && !isNaN(minPrice)) {
      whereConditions.push('product_price >= ?');
      params.push(parseFloat(minPrice));
    }

    if (maxPrice && !isNaN(maxPrice)) {
      whereConditions.push('product_price < ?');
      params.push(parseFloat(maxPrice));
    }

    // 佣金筛选 - 大于
    if (minCommission && !isNaN(minCommission)) {
      whereConditions.push('commission_amount > ?');
      params.push(parseFloat(minCommission));
    }

    // 365天销量区间筛选
    if (minSales365 && !isNaN(minSales365)) {
      whereConditions.push('sales_365_days >= ?');
      params.push(parseInt(minSales365));
    }

    if (maxSales365 && !isNaN(maxSales365)) {
      whereConditions.push('sales_365_days <= ?');
      params.push(parseInt(maxSales365));
    }

    // 7天增长率筛选 - 大于
    if (minGrowthRate7Days && !isNaN(minGrowthRate7Days)) {
      whereConditions.push('sales_7_days_growth_rate > ?');
      params.push(parseFloat(minGrowthRate7Days));
    }

    if (productSources && productSources.trim() !== '') {
      const sources = productSources.split(',').map(s => s.trim()).filter(s => s);
      if (sources.length > 0) {
        const placeholders = sources.map(() => '?').join(',');
        whereConditions.push(`product_source IN (${placeholders})`);
        params.push(...sources);
      }
    }

    if (allianceChannels && allianceChannels.trim() !== '') {
      const channels = allianceChannels.split(',').map(c => c.trim()).filter(c => c);
      if (channels.length > 0) {
        const placeholders = channels.map(() => '?').join(',');
        whereConditions.push(`alliance_channel IN (${placeholders})`);
        params.push(...channels);
      }
    }

    // 标签多选筛选
    if (tags && tags.trim() !== '') {
      const tagList = tags.split(',').map(t => t.trim()).filter(t => t && !isNaN(t));
      if (tagList.length > 0) {
        const placeholders = tagList.map(() => '?').join(',');
        whereConditions.push(`tag IN (${placeholders})`);
        params.push(...tagList.map(t => parseInt(t)));
      }
    }

    // 产品类型多选筛选
    if (productTypes && productTypes.trim() !== '') {
      const typeList = productTypes.split(',').map(t => t.trim()).filter(t => t);
      if (typeList.length > 0) {
        const placeholders = typeList.map(() => '?').join(',');
        whereConditions.push(`product_type IN (${placeholders})`);
        params.push(...typeList);
      }
    }

    if (batchNumber && batchNumber.trim() !== '') {
      whereConditions.push('batch_number LIKE ?');
      params.push(`%${batchNumber.trim()}%`);
    }

    if (startDate && startDate.trim() !== '') {
      whereConditions.push('DATE(created_at) >= ?');
      params.push(startDate.trim());
    }

    // 报表日期筛选
    if (date && date.trim() !== '') {
      whereConditions.push('date = ?');
      params.push(date.trim());
    }

    if (endDate && endDate.trim() !== '') {
      whereConditions.push('DATE(created_at) <= ?');
      params.push(endDate.trim());
    }

    // 用户权限筛选
    if (req.authResult.userType === 'anchor' && req.authResult.anchorInfo) {
      whereConditions.push('anchor_name = ?');
      params.push(req.authResult.anchorInfo.anchor_name);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 获取所有产品数据用于导出
    const exportQuery = `
      SELECT * FROM products
      ${whereClause}
      ORDER BY created_at DESC
    `;

    const productsResult = await database.prepare(exportQuery).all(params);
    const products = productsResult.results || [];

    res.json({
      products: products,
      exportTime: new Date().toISOString()
    });

  } catch (error) {
    console.error("Error exporting products:", error);
    res.status(500).json({
      error: "Failed to export products",
      message: error.message
    });
  }
}

// 处理批量产品数据添加请求
async function handleProductsBatch(req, res) {
  try {
    const { products } = req.body;

    // 验证请求数据
    if (!products || !Array.isArray(products) || products.length === 0) {
      return res.status(400).json({
        error: "Invalid request",
        message: "products字段必须是非空数组"
      });
    }

    // 生成批次号 (格式: YYYYMMDD-XXX)
    const batchNumber = await generateBatchNumber();

    // 验证每个产品的必填字段
    const requiredFields = ['anchor_name', 'product_id', 'product_title', 'product_price'];
    const validationErrors = [];

    products.forEach((product, index) => {
      requiredFields.forEach(field => {
        if (!product[field]) {
          validationErrors.push(`产品${index + 1}: 缺少必填字段 ${field}`);
        }
      });

      // 验证价格是否为有效数字
      if (product.product_price && (isNaN(product.product_price) || product.product_price < 0)) {
        validationErrors.push(`产品${index + 1}: product_price必须是有效的正数`);
      }

      // 验证佣金率
      if (product.commission_rate && (isNaN(product.commission_rate) || product.commission_rate < 0 || product.commission_rate > 100)) {
        validationErrors.push(`产品${index + 1}: commission_rate必须是0-100之间的数字`);
      }
    });

    if (validationErrors.length > 0) {
      return res.status(400).json({
        error: "Validation failed",
        message: "数据验证失败",
        details: validationErrors
      });
    }

    // 准备插入数据
    const insertSQL = `
      INSERT INTO products (
        anchor_name, product_id, product_title, product_price, commission_rate, commission_amount,
        product_source, alliance_channel, product_category, sales_365_days, sales_30_days, sales_7_days,
        sales_7_days_growth_rate, orders_30_days, anchor_sales_30_days,
        shop_name, safe_shop_id, feature_tags, tag, product_type, date, batch_number
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const insertedProducts = [];
    const errors = [];

    // 使用事务批量插入
    try {
      await database.exec('BEGIN TRANSACTION');

      for (let i = 0; i < products.length; i++) {
        const product = products[i];

        try {
          // 准备插入参数
          // 自动生成当前日期 (YYYY-MM-DD 格式)
          const currentDate = new Date().toISOString().split('T')[0];

          const params = [
            product.anchor_name,
            product.product_id,
            product.product_title,
            parseFloat(product.product_price),
            product.commission_rate ? parseFloat(product.commission_rate) : null,
            product.commission_amount ? parseFloat(product.commission_amount) : null,
            product.product_source || null,
            product.alliance_channel || null,
            product.product_category || null,
            product.sales_365_days ? parseInt(product.sales_365_days) : 0,
            product.sales_30_days ? parseInt(product.sales_30_days) : 0,
            product.sales_7_days ? parseInt(product.sales_7_days) : 0,
            product.sales_7_days_growth_rate ? parseFloat(product.sales_7_days_growth_rate) : null,
            product.orders_30_days ? parseInt(product.orders_30_days) : 0,
            product.anchor_sales_30_days ? parseFloat(product.anchor_sales_30_days) : 0,
            product.shop_name || null,
            product.safe_shop_id || null,
            product.feature_tags || null,
            product.tag || null,
            product.product_type || null,
            product.date || currentDate, // 使用传入的日期或当前日期
            batchNumber
          ];

          await database.run(insertSQL, params);
          insertedProducts.push({
            ...product,
            batch_number: batchNumber
          });

        } catch (error) {
          console.error(`插入产品${i + 1}失败:`, error);
          errors.push({
            index: i + 1,
            product_id: product.product_id,
            error: error.message
          });
        }
      }

      await database.exec('COMMIT');

    } catch (error) {
      await database.exec('ROLLBACK');
      throw error;
    }

    // 返回结果
    const result = {
      success: true,
      batch_number: batchNumber,
      total_received: products.length,
      total_inserted: insertedProducts.length,
      total_errors: errors.length,
      inserted_products: insertedProducts
    };

    if (errors.length > 0) {
      result.errors = errors;
      result.message = `批量添加完成，成功${insertedProducts.length}条，失败${errors.length}条`;
    } else {
      result.message = `批量添加成功，共添加${insertedProducts.length}条产品`;
    }

    res.json(result);

  } catch (error) {
    console.error("Error handling batch products:", error);
    res.status(500).json({
      error: "Failed to process batch products",
      message: error.message
    });
  }
}

// 生成批次号
async function generateBatchNumber() {
  try {
    // 获取当前日期 (YYYYMMDD格式)
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const dateStr = `${year}${month}${day}`;

    // 查询当天已有的最大批次号
    const query = `
      SELECT batch_number
      FROM products
      WHERE batch_number LIKE ?
      ORDER BY batch_number DESC
      LIMIT 1
    `;

    const result = await database.get(query, [`${dateStr}-%`]);

    let sequence = 1;
    if (result && result.batch_number) {
      // 提取序号部分并加1
      const parts = result.batch_number.split('-');
      if (parts.length === 2) {
        const lastSequence = parseInt(parts[1]);
        if (!isNaN(lastSequence)) {
          sequence = lastSequence + 1;
        }
      }
    }

    // 生成新的批次号 (序号补零到3位)
    const batchNumber = `${dateStr}-${String(sequence).padStart(3, '0')}`;

    return batchNumber;

  } catch (error) {
    console.error('生成批次号失败:', error);
    // 如果生成失败，使用时间戳作为后备方案
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const timestamp = now.getTime().toString().slice(-3);
    return `${year}${month}${day}-${timestamp}`;
  }
}

// ==================== 选品规则相关处理函数 ====================

// 获取所有选品规则
async function handleGetSelectionRules(req, res) {
  try {
    // 首先确保表存在
    try {
      await database.exec(`
        CREATE TABLE IF NOT EXISTS selection_rules (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          rule_name TEXT NOT NULL,
          description TEXT,
          total_target INTEGER DEFAULT 500,
          rule_groups TEXT NOT NULL,
          comprehensive_config TEXT,
          status TEXT DEFAULT 'active',
          created_by TEXT DEFAULT 'user',
          last_used_at DATETIME,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
    } catch (createError) {
      console.error("Error creating selection_rules table:", createError);
    }

    // 检查并添加comprehensive_config字段（如果不存在）
    try {
      // 先检查字段是否存在
      const checkResult = await database.all("PRAGMA table_info(selection_rules)");
      const columns = checkResult.results || [];
      const hasComprehensiveConfig = columns.some(col => col.name === 'comprehensive_config');
      
      if (!hasComprehensiveConfig) {
        console.log("添加comprehensive_config字段到selection_rules表...");
        await database.exec("ALTER TABLE selection_rules ADD COLUMN comprehensive_config TEXT");
        console.log("✅ comprehensive_config字段添加成功");
      }
    } catch (alterError) {
      console.error("Error adding comprehensive_config column:", alterError);
      // 如果添加字段失败，继续执行，但不包含comprehensive_config字段
    }

    // 获取查询参数
    const page = parseInt(req.query.page) || CONSTANTS.PAGINATION.DEFAULT_PAGE;
    const limit = parseInt(req.query.limit) || CONSTANTS.PAGINATION.DEFAULT_LIMIT;
    const offset = (page - 1) * limit;

    // 构建WHERE条件
    let whereConditions = [];
    let queryParams = [];

    // 状态筛选
    const statusFilters = req.query.status;
    if (statusFilters) {
      const statuses = Array.isArray(statusFilters) ? statusFilters : [statusFilters];
      if (statuses.length > 0) {
        const statusPlaceholders = statuses.map(() => '?').join(',');
        whereConditions.push(`status IN (${statusPlaceholders})`);
        queryParams.push(...statuses);
      }
    } else {
      // 默认只显示活跃规则
      whereConditions.push('status = ?');
      queryParams.push('active');
    }

    // 规则名称筛选
    if (req.query.ruleName) {
      whereConditions.push('rule_name LIKE ?');
      queryParams.push(`%${req.query.ruleName}%`);
    }

    // 创建者筛选
    if (req.query.createdBy) {
      whereConditions.push('created_by = ?');
      queryParams.push(req.query.createdBy);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 获取总数
    const countResult = await database.get(`
      SELECT COUNT(*) as total
      FROM selection_rules
      ${whereClause}
    `, queryParams);

    const total = countResult?.total || 0;

    // 获取分页数据
    const result = await database.all(`
      SELECT id, rule_name, description, total_target, status, created_by,
             last_used_at, created_at, updated_at, rule_groups, comprehensive_config
      FROM selection_rules
      ${whereClause}
      ORDER BY last_used_at DESC, created_at DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, limit, offset]);

    res.json({
      success: true,
      data: result.results || [],
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error("Error fetching selection rules:", error);
    
    // 如果是表不存在的错误，返回空数组
    if (error.message && error.message.includes('no such table')) {
      res.json({
        success: true,
        data: []
      });
    } else {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }
}

// 创建选品规则
async function handleCreateSelectionRule(req, res) {
  try {
    // 首先确保表存在
    try {
      await database.exec(`
        CREATE TABLE IF NOT EXISTS selection_rules (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          rule_name TEXT NOT NULL,
          description TEXT,
          total_target INTEGER DEFAULT 500,
          rule_groups TEXT NOT NULL,
          comprehensive_config TEXT,
          status TEXT DEFAULT 'active',
          created_by TEXT DEFAULT 'user',
          last_used_at DATETIME,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
    } catch (createError) {
      console.error("Error creating selection_rules table:", createError);
    }

    // 检查并添加comprehensive_config字段（如果不存在）
    try {
      // 先检查字段是否存在
      const checkResult = await database.all("PRAGMA table_info(selection_rules)");
      const columns = checkResult.results || [];
      const hasComprehensiveConfig = columns.some(col => col.name === 'comprehensive_config');
      
      if (!hasComprehensiveConfig) {
        console.log("添加comprehensive_config字段到selection_rules表...");
        await database.exec("ALTER TABLE selection_rules ADD COLUMN comprehensive_config TEXT");
        console.log("✅ comprehensive_config字段添加成功");
      }
    } catch (alterError) {
      console.error("Error adding comprehensive_config column:", alterError);
      // 如果添加字段失败，继续执行，但不包含comprehensive_config字段
    }

    const { rule_name, description, total_target, rule_groups, comprehensive_config } = req.body;

    if (!rule_name || !rule_groups) {
      return res.status(400).json({
        success: false,
        message: "规则名称和规则组配置为必填项"
      });
    }

    // 验证rule_groups是否为有效的JSON
    let parsedGroups;
    try {
      parsedGroups = typeof rule_groups === 'string' ? JSON.parse(rule_groups) : rule_groups;
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: "规则组配置格式无效"
      });
    }

    // 验证comprehensive_config是否为有效的JSON
    let parsedComprehensiveConfig = null;
    if (comprehensive_config) {
      try {
        parsedComprehensiveConfig = typeof comprehensive_config === 'string' ? JSON.parse(comprehensive_config) : comprehensive_config;
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: "综合配置格式无效"
        });
      }
    }

    const result = await database.run(`
      INSERT INTO selection_rules (rule_name, description, total_target, rule_groups, comprehensive_config, created_by)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [
      rule_name,
      description || null,
      total_target || 500,
      JSON.stringify(parsedGroups),
      parsedComprehensiveConfig ? JSON.stringify(parsedComprehensiveConfig) : null,
      'user'
    ]);

    res.json({
      success: true,
      data: {
        id: result.meta.last_row_id,
        rule_name,
        description,
        total_target: total_target || 500
      }
    });
  } catch (error) {
    console.error("Error creating selection rule:", error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
}

// 更新选品规则
async function handleUpdateSelectionRule(req, res) {
  try {
    const { ruleId } = req.params;
    const { rule_name, description, total_target, rule_groups, comprehensive_config } = req.body;

    if (!rule_name || !rule_groups) {
      return res.status(400).json({
        success: false,
        message: "规则名称和规则组配置为必填项"
      });
    }

    // 验证rule_groups是否为有效的JSON
    let parsedGroups;
    try {
      parsedGroups = typeof rule_groups === 'string' ? JSON.parse(rule_groups) : rule_groups;
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: "规则组配置格式无效"
      });
    }

    // 验证comprehensive_config是否为有效的JSON
    let parsedComprehensiveConfig = null;
    if (comprehensive_config) {
      try {
        parsedComprehensiveConfig = typeof comprehensive_config === 'string' ? JSON.parse(comprehensive_config) : comprehensive_config;
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: "综合配置格式无效"
        });
      }
    }

    // 检查规则是否存在
    const existingRule = await database.get(`
      SELECT id FROM selection_rules WHERE id = ? AND status = 'active'
    `, [ruleId]);

    if (!existingRule) {
      return res.status(404).json({
        success: false,
        message: "规则不存在"
      });
    }

    const result = await database.run(`
      UPDATE selection_rules
      SET rule_name = ?, description = ?, total_target = ?, rule_groups = ?,
          comprehensive_config = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND status = 'active'
    `, [
      rule_name,
      description || null,
      total_target || 500,
      JSON.stringify(parsedGroups),
      parsedComprehensiveConfig ? JSON.stringify(parsedComprehensiveConfig) : null,
      ruleId
    ]);

    if (result.meta.changes === 0) {
      return res.status(404).json({
        success: false,
        message: "规则不存在或更新失败"
      });
    }

    res.json({
      success: true,
      data: {
        id: ruleId,
        rule_name,
        description,
        total_target: total_target || 500
      }
    });
  } catch (error) {
    console.error("Error updating selection rule:", error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
}

// 获取单个选品规则
async function handleGetSelectionRule(req, res) {
  try {
    const { ruleId } = req.params;

    const result = await database.get(`
      SELECT * FROM selection_rules WHERE id = ? AND status = 'active'
    `, [ruleId]);

    if (!result) {
      return res.status(404).json({
        success: false,
        message: "规则不存在"
      });
    }

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error("Error fetching selection rule:", error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
}

// 删除选品规则
async function handleDeleteSelectionRule(req, res) {
  try {
    const { ruleId } = req.params;

    const result = await database.run(`
      UPDATE selection_rules SET status = 'inactive' WHERE id = ?
    `, [ruleId]);

    if (result.meta.changes === 0) {
      return res.status(404).json({
        success: false,
        message: "规则不存在"
      });
    }

    res.json({
      success: true,
      message: "规则删除成功"
    });
  } catch (error) {
    console.error("Error deleting selection rule:", error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
}

// 预览选品规则结果
async function handlePreviewSelectionRule(req, res) {
  try {
    const { conditions, orderBy, orderDirection, limit, randomSelection, anchor_id, date } = req.body;

    if (!conditions || !Array.isArray(conditions)) {
      return res.status(400).json({
        success: false,
        message: "筛选条件无效"
      });
    }

    // 构建WHERE条件
    const { whereClause, params } = buildWhereClause(conditions);

    // 添加主播和日期筛选条件
    let finalWhereClause = whereClause;
    let finalParams = [...params];

    if (anchor_id && date) {
      // 获取主播名称
      const anchorQuery = 'SELECT anchor_name FROM anchors WHERE id = ?';
      const anchorResult = await database.get(anchorQuery, [anchor_id]);

      if (anchorResult) {
        const anchorName = anchorResult.anchor_name;

        // 添加主播和日期条件
        const additionalConditions = [];
        if (finalWhereClause) {
          additionalConditions.push(finalWhereClause.replace('WHERE ', ''));
        }
        additionalConditions.push('anchor_name = ?');
        additionalConditions.push('date = ?');

        finalWhereClause = `WHERE ${additionalConditions.join(' AND ')}`;
        finalParams.push(anchorName, date);
      }
    }

    // 验证排序字段
    const validOrderFields = [
      'sales_7_days_growth_rate', 'commission_amount', 'sales_365_days',
      'anchor_sales_30_days', 'orders_30_days', 'product_price', 'created_at'
    ];
    const finalOrderBy = validOrderFields.includes(orderBy) ? orderBy : 'sales_7_days_growth_rate';
    const finalOrderDirection = ['ASC', 'DESC'].includes(orderDirection?.toUpperCase()) ? orderDirection.toUpperCase() : 'DESC';

    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM products ${finalWhereClause}`;
    const countResult = await database.get(countQuery, finalParams);
    const total = countResult.total || 0;

    let finalProducts = [];
    const targetLimit = limit || 50;

    if (randomSelection && total > targetLimit) {
      // 随机选取模式：先获取更多数据，然后随机选择
      const fetchLimit = Math.min(total, targetLimit * 3); // 获取目标数量的3倍用于随机选择

      const dataQuery = `
        SELECT *
        FROM products
        ${finalWhereClause}
        ORDER BY ${finalOrderBy} ${finalOrderDirection}
        LIMIT ?
      `;
      const dataResult = await database.all(dataQuery, [...finalParams, fetchLimit]);
      const allCandidates = dataResult.results || [];

      // 随机选择指定数量的产品
      if (allCandidates.length > 0) {
        const shuffled = [...allCandidates].sort(() => 0.5 - Math.random());
        finalProducts = shuffled.slice(0, targetLimit);
      }
    } else {
      // 正常排序模式
      const dataQuery = `
        SELECT *
        FROM products
        ${finalWhereClause}
        ORDER BY ${finalOrderBy} ${finalOrderDirection}
        LIMIT ?
      `;
      const dataResult = await database.all(dataQuery, [...finalParams, targetLimit]);
      finalProducts = dataResult.results || [];
    }

    res.json({
      success: true,
      data: {
        total,
        products: finalProducts,
        randomSelection: randomSelection || false
      }
    });
  } catch (error) {
    console.error("Error previewing selection rule:", error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
}

// 预览所有选品规则组的综合结果
async function handlePreviewAllSelectionRules(req, res) {
    try {
        const { groups, comprehensiveConfig, anchor_id, date } = req.body;

        if (!groups || !Array.isArray(groups) || groups.length === 0) {
            return res.status(400).json({
                success: false,
                message: '请提供有效的规则组配置'
            });
        }

        // 获取主播名称（如果提供了主播ID和日期）
        let anchorName = null;
        if (anchor_id && date) {
            const anchorQuery = 'SELECT anchor_name FROM anchors WHERE id = ?';
            const anchorResult = await database.get(anchorQuery, [anchor_id]);
            if (anchorResult) {
                anchorName = anchorResult.anchor_name;
                console.log('应用筛选条件 - 主播:', anchorName, '日期:', date);
            }
        }

        console.log('开始处理综合预览请求...');
        console.log('规则组数量:', groups.length);
        console.log('综合配置:', comprehensiveConfig);

        // 处理每个组并获取产品
        const groupResults = [];
        
        // 按优先级排序组（优先级低的数字在前）
        const sortedGroups = groups.map((group, index) => ({
            ...group,
            originalIndex: index,
            priority: group.positionConfig?.priority || (index + 1)
        })).sort((a, b) => a.priority - b.priority);

        console.log('按优先级排序后的组:', sortedGroups.map(g => `${g.name}(优先级:${g.priority})`));

        // 计算每个组的实际位置范围（按优先级顺序重新分配位置）
        let currentGlobalPosition = 1;
        const groupPositionMap = new Map();

        sortedGroups.forEach(group => {
            const targetCount = parseInt(group.target) || 50;
            groupPositionMap.set(group.originalIndex, {
                startPosition: currentGlobalPosition,
                endPosition: currentGlobalPosition + targetCount - 1
            });
            currentGlobalPosition += targetCount;
            console.log(`组 ${group.name} (优先级: ${group.priority}) 分配位置范围: ${groupPositionMap.get(group.originalIndex).startPosition}-${groupPositionMap.get(group.originalIndex).endPosition}`);
        });

        // 第一步：查询每个组的所有候选数据（不去重）
        const groupCandidatesMap = new Map();

        for (const group of sortedGroups) {
            console.log(`\n第一步：查询组 ${group.name} (优先级: ${group.priority}) 的所有候选数据`);

            // 构建WHERE条件
            const { whereClause, params } = buildWhereClause(group.conditions || []);

            // 添加主播和日期筛选条件
            let finalWhereClause = whereClause;
            let finalParams = [...params];

            if (anchorName && date) {
                const additionalConditions = [];
                if (finalWhereClause) {
                    additionalConditions.push(finalWhereClause.replace('WHERE ', ''));
                }
                additionalConditions.push('anchor_name = ?');
                additionalConditions.push('date = ?');

                finalWhereClause = `WHERE ${additionalConditions.join(' AND ')}`;
                finalParams.push(anchorName, date);
            }

            // 验证排序字段
            const validOrderFields = [
                'sales_7_days_growth_rate', 'commission_amount', 'sales_365_days',
                'anchor_sales_30_days', 'orders_30_days', 'product_price', 'created_at'
            ];
            const finalOrderBy = validOrderFields.includes(group.orderBy) ? group.orderBy : 'sales_7_days_growth_rate';
            const finalOrderDirection = ['ASC', 'DESC'].includes(group.orderDirection?.toUpperCase()) ? group.orderDirection.toUpperCase() : 'DESC';

            // 查询该组的所有候选数据
            const query = `
                SELECT * FROM products
                ${finalWhereClause}
                ORDER BY ${finalOrderBy} ${finalOrderDirection}
            `;

            console.log(`组 ${group.name} 查询SQL:`, query);
            console.log('查询参数:', finalParams);

            const results = await database.all(query, finalParams);
            const allCandidates = results.results || [];
            console.log(`组 ${group.name} 查询到 ${allCandidates.length} 个候选产品`);

            // 为每个产品添加组信息
            const candidatesWithGroupInfo = allCandidates.map(product => ({
                ...product,
                source_group_name: group.name,
                source_group_index: group.originalIndex,
                source_group_priority: group.priority,
                source_group_random: group.positionConfig?.randomSelection || false
            }));

            groupCandidatesMap.set(group.originalIndex, candidatesWithGroupInfo);
        }

        // 第二步：简化的产品分配逻辑
        console.log('\n第二步：开始产品分配');

        // 全局已选择的产品ID集合，确保不重复
        const globalSelectedIds = new Set();

        // 第三步：为每个组分配指定数量的产品

        for (const group of sortedGroups) {
            console.log(`\n第三步：为组 ${group.name} (优先级: ${group.priority}) 分配产品`);

            const targetCount = parseInt(group.target) || 50;

            // 获取该组的候选产品
            let availableCandidates = groupCandidatesMap.get(group.originalIndex) || [];

            // 如果启用去重，过滤掉已被选择的产品
            if (comprehensiveConfig?.deduplicationEnabled) {
                availableCandidates = availableCandidates.filter(product => !globalSelectedIds.has(product.product_id));
            }

            console.log(`组 ${group.name} 可用候选产品: ${availableCandidates.length}`);

            let selectedProducts = [];

            // 如果启用随机选取
            if (group.positionConfig?.randomSelection && availableCandidates.length > targetCount) {
                const shuffled = [...availableCandidates].sort(() => 0.5 - Math.random());
                selectedProducts = shuffled.slice(0, targetCount);
                console.log(`组 ${group.name} 随机选取了 ${selectedProducts.length} 个产品`);
            } else {
                selectedProducts = availableCandidates.slice(0, targetCount);
                console.log(`组 ${group.name} 按排序选取了 ${selectedProducts.length} 个产品`);
            }

            // 数量保证：如果数量不足且启用去重，从规则组1补充
            if (selectedProducts.length < targetCount && comprehensiveConfig?.deduplicationEnabled && group.priority !== 1) {
                const shortfall = targetCount - selectedProducts.length;
                console.log(`组 ${group.name} 数量不足，需要补充 ${shortfall} 个产品`);

                // 找到优先级1组
                const priority1Group = sortedGroups.find(g => g.priority === 1);
                if (priority1Group) {
                    let priority1Candidates = groupCandidatesMap.get(priority1Group.originalIndex) || [];

                    // 过滤掉已被选择的产品
                    priority1Candidates = priority1Candidates.filter(product =>
                        !globalSelectedIds.has(product.product_id) &&
                        !selectedProducts.some(selected => selected.product_id === product.product_id)
                    );

                    const supplementProducts = priority1Candidates.slice(0, shortfall);

                    // 为补充的产品标记并立即记录到全局已选择列表
                    supplementProducts.forEach(product => {
                        product.is_supplement = true;
                        globalSelectedIds.add(product.product_id);  // 立即记录，防止重复
                        console.log(`🔄 补充产品 ${product.product_id} 从规则组1到组 ${group.name}`);
                    });

                    selectedProducts.push(...supplementProducts);
                    console.log(`从优先级1组补充了 ${supplementProducts.length} 个产品到组 ${group.name}`);
                }
            }

            // 记录已选择的产品ID（用于去重）
            selectedProducts.forEach(product => {
                if (comprehensiveConfig?.deduplicationEnabled) {
                    if (globalSelectedIds.has(product.product_id)) {
                        console.log(`⚠️ 警告：产品 ${product.product_id} 已经被选择过了！当前组: ${group.name}`);
                    }
                    globalSelectedIds.add(product.product_id);
                    console.log(`✅ 记录产品 ${product.product_id} 到全局已选择列表，当前组: ${group.name}`);
                }
            });

            console.log(`组 ${group.name} 当前已选择产品数量: ${selectedProducts.length}`);
            console.log(`全局已选择产品总数: ${globalSelectedIds.size}`);

            // 获取该组的实际位置范围
            const actualPositionRange = groupPositionMap.get(group.originalIndex);

            // 为产品添加最终的组信息和位置信息
            const productsWithGroupInfo = selectedProducts.map((product, index) => ({
                ...product,
                group_name: group.name,
                group_index: group.originalIndex,
                priority: group.priority,
                position_in_group: index + 1,
                global_position: actualPositionRange.startPosition + index,
                original_start_position: group.positionConfig?.startPosition || 1,
                actual_start_position: actualPositionRange.startPosition
            }));

            groupResults.push({
                groupIndex: group.originalIndex,
                name: group.name,
                priority: group.priority,
                target: targetCount,
                actualCount: selectedProducts.length,
                deduplicatedCount: comprehensiveConfig?.deduplicationEnabled ?
                    (groupCandidatesMap.get(group.originalIndex)?.length || 0) - selectedProducts.length : 0,
                coverage: targetCount > 0 ? Math.round((selectedProducts.length / targetCount) * 100) : 0,
                randomSelection: group.positionConfig?.randomSelection || false,
                positionConfig: {
                    ...group.positionConfig,
                    actualStartPosition: actualPositionRange.startPosition,
                    actualEndPosition: actualPositionRange.endPosition
                },
                products: productsWithGroupInfo
            });

            console.log(`组 ${group.name} 最终选择了 ${selectedProducts.length} 个产品，位置范围: ${actualPositionRange.startPosition}-${actualPositionRange.startPosition + selectedProducts.length - 1}`);
        }

        // 按原始组顺序重新排序结果
        groupResults.sort((a, b) => a.groupIndex - b.groupIndex);

        // 合并所有产品并按位置排序
        let allProducts = [];
        groupResults.forEach(group => {
            allProducts = [...allProducts, ...group.products];
        });

        // 实现位置排序和跨组打乱功能
        if (comprehensiveConfig?.shuffleGroups && comprehensiveConfig.shuffleGroups.length >= 2) {
            console.log('启用跨组打乱功能，打乱的组索引:', comprehensiveConfig.shuffleGroups);

            // 分离需要打乱的组和不需要打乱的组
            const shuffleGroupIndexes = new Set(comprehensiveConfig.shuffleGroups);
            const shuffleProducts = [];  // 需要打乱的产品
            const normalProducts = [];   // 不需要打乱的产品

            allProducts.forEach(product => {
                if (shuffleGroupIndexes.has(product.group_index)) {
                    shuffleProducts.push(product);
                } else {
                    normalProducts.push(product);
                }
            });

            console.log(`需要打乱的产品数量: ${shuffleProducts.length}`);
            console.log(`不需要打乱的产品数量: ${normalProducts.length}`);

            // 对需要打乱的产品进行随机排序
            const shuffledProducts = [...shuffleProducts].sort(() => 0.5 - Math.random());

            // 重新分配位置：为打乱的产品重新分配连续的位置
            let shuffleStartPosition = Math.min(...shuffleProducts.map(p => p.global_position));
            shuffledProducts.forEach((product, index) => {
                product.global_position = shuffleStartPosition + index;
                product.is_shuffled = true;  // 标记为已打乱
            });

            // 重新合并产品列表
            allProducts = [...shuffledProducts, ...normalProducts];

            // 按最终位置排序
            allProducts.sort((a, b) => a.global_position - b.global_position);

            console.log('跨组打乱完成，重新分配位置');
        } else {
            // 根据位置策略排序最终产品列表
            switch (comprehensiveConfig?.positionStrategy) {
                case 'priority':
                    // 按优先级排序，同优先级内按组内位置排序
                    allProducts.sort((a, b) => {
                        if (a.priority !== b.priority) {
                            return a.priority - b.priority;
                        }
                        return a.position_in_group - b.position_in_group;
                    });
                    break;
                case 'position':
                    // 按全局位置排序
                    allProducts.sort((a, b) => a.global_position - b.global_position);
                    break;
                case 'mixed':
                    // 混合策略：先按优先级，再按全局位置
                    allProducts.sort((a, b) => {
                        if (a.priority !== b.priority) {
                            return a.priority - b.priority;
                        }
                        return a.global_position - b.global_position;
                    });
                    break;
                default:
                    // 默认按位置排序
                    allProducts.sort((a, b) => a.global_position - b.global_position);
            }
        }

        // 检查冲突
        const conflicts = [];
        
        // 检查重复产品
        const productIdCounts = {};
        const duplicateDetails = {};

        allProducts.forEach((product, index) => {
            const productId = product.product_id;
            if (productIdCounts[productId]) {
                productIdCounts[productId]++;
                if (!duplicateDetails[productId]) {
                    duplicateDetails[productId] = [];
                }
                duplicateDetails[productId].push({
                    index: index,
                    group: product.group_name,
                    priority: product.priority,
                    global_position: product.global_position,
                    is_supplement: product.is_supplement || false
                });
            } else {
                productIdCounts[productId] = 1;
                duplicateDetails[productId] = [{
                    index: index,
                    group: product.group_name,
                    priority: product.priority,
                    global_position: product.global_position,
                    is_supplement: product.is_supplement || false
                }];
            }
        });

        console.log('\n=== 重复产品检查 ===');
        Object.entries(productIdCounts).forEach(([productId, count]) => {
            if (count > 1) {
                console.log(`🔴 发现重复产品 ${productId}，出现 ${count} 次:`);
                duplicateDetails[productId].forEach(detail => {
                    console.log(`  - 索引: ${detail.index}, 组: ${detail.group}, 优先级: ${detail.priority}, 位置: ${detail.global_position}, 补充: ${detail.is_supplement}`);
                });

                conflicts.push({
                    type: 'duplicate',
                    message: `产品 ${productId} 在多个规则组中被选中${comprehensiveConfig?.deduplicationEnabled ? '（去重功能可能存在问题）' : ''}`
                });
            }
        });

        console.log(`总产品数: ${allProducts.length}, 唯一产品数: ${Object.keys(productIdCounts).length}, 重复产品数: ${Object.values(productIdCounts).filter(count => count > 1).length}`);

        // 计算总数
        const totalSelected = allProducts.length;
        
        // 计算整体统计
        const statistics = {
            overallScore: totalSelected > 0 ? Math.round((totalSelected / (groups.reduce((sum, g) => sum + (parseInt(g.target) || 50), 0))) * 100) : 0,
            totalGroups: groups.length,
            totalSelected: totalSelected,
            avgPrice: allProducts.length > 0 ? allProducts.reduce((sum, p) => sum + (parseFloat(p.product_price) || 0), 0) / allProducts.length : 0,
            avgCommission: allProducts.length > 0 ? allProducts.reduce((sum, p) => sum + (parseFloat(p.commission_amount) || 0), 0) / allProducts.length : 0
        };

        console.log('\n=== 最终结果统计 ===');
        console.log(`总共处理了 ${groups.length} 个规则组`);
        console.log(`总共选择了 ${totalSelected} 个产品`);
        console.log(`是否启用去重: ${comprehensiveConfig?.deduplicationEnabled ? '是' : '否'}`);
        console.log(`位置策略: ${comprehensiveConfig?.positionStrategy || 'position'}`);
        console.log(`是否启用跨组打乱: ${comprehensiveConfig?.shuffleGroups?.length >= 2 ? '是' : '否'}`);
        console.log(`重复产品数量: ${Object.values(productIdCounts).filter(count => count > 1).length}`);

        // 调试：显示前10个产品的位置信息
        console.log('\n=== 前10个产品的位置信息 ===');
        allProducts.slice(0, 10).forEach((product, index) => {
            console.log(`${index + 1}. 产品ID: ${product.product_id}, 组: ${product.group_name}, 优先级: ${product.priority}, 全局位置: ${product.global_position}, 组内位置: ${product.position_in_group}${product.is_shuffled ? ' [已打乱]' : ''}`);
        });

        res.json({
            success: true,
            data: {
                groups: groupResults,
                total: totalSelected,
                conflicts: conflicts,
                statistics: statistics
            }
        });

    } catch (error) {
        console.error('处理综合预览时发生错误:', error);
        res.status(500).json({
            success: false,
            message: '处理综合预览时发生错误',
            error: error.message
        });
    }
}

// 执行选品规则
async function handleExecuteSelectionRule(req, res) {
    try {
        const { rule_name, description, total_target, rule_groups, comprehensiveConfig } = req.body;

        if (!rule_name || !rule_groups || !Array.isArray(rule_groups)) {
            return res.status(400).json({
                success: false,
                message: "规则名称和规则组配置为必填项"
            });
        }

        console.log('开始执行选品规则:', rule_name);
        console.log('规则组数量:', rule_groups.length);

        // 生成批次号
        const batchNumber = await generateBatchNumber();
        const executionTime = new Date().toISOString();

        let totalSelected = 0;
        const groupResults = [];
        const allSelectedProductIds = [];

        try {
            await database.exec('BEGIN TRANSACTION');

            // 处理每个组并获取产品
            
            // 按优先级排序组（优先级低的数字在前）
            const sortedGroups = rule_groups.map((group, index) => ({
                ...group,
                originalIndex: index,
                priority: group.positionConfig?.priority || (index + 1)
            })).sort((a, b) => a.priority - b.priority);

            console.log('执行时按优先级排序后的组:', sortedGroups.map(g => `${g.name}(优先级:${g.priority})`));

            // 存储每个组的产品，用于最终的位置排序和跨组打乱
            const groupProductsMap = new Map();

            // 计算每个组的实际位置范围（按优先级顺序重新分配位置）
            let currentGlobalPosition = 1;
            const groupPositionMap = new Map();

            sortedGroups.forEach(group => {
                const targetCount = parseInt(group.target) || 50;
                groupPositionMap.set(group.originalIndex, {
                    startPosition: currentGlobalPosition,
                    endPosition: currentGlobalPosition + targetCount - 1
                });
                currentGlobalPosition += targetCount;
                console.log(`执行时组 ${group.name} (优先级: ${group.priority}) 分配位置范围: ${groupPositionMap.get(group.originalIndex).startPosition}-${groupPositionMap.get(group.originalIndex).endPosition}`);
            });

            // 第一步：查询每个组的所有候选数据（不去重）
            const groupCandidatesMap = new Map();

            for (const group of sortedGroups) {
                console.log(`\n执行时第一步：查询组 ${group.name} (优先级: ${group.priority}) 的所有候选数据`);

                // 构建WHERE条件
                const { whereClause, params } = buildWhereClause(group.conditions || []);

                // 验证排序字段
                const validOrderFields = [
                    'sales_7_days_growth_rate', 'commission_amount', 'sales_365_days',
                    'anchor_sales_30_days', 'orders_30_days', 'product_price', 'created_at'
                ];
                const finalOrderBy = validOrderFields.includes(group.orderBy) ? group.orderBy : 'sales_7_days_growth_rate';
                const finalOrderDirection = ['ASC', 'DESC'].includes(group.orderDirection?.toUpperCase()) ? group.orderDirection.toUpperCase() : 'DESC';

                // 查询该组的所有候选数据（注意：这里查询完整产品信息，不只是ID）
                const query = `
                    SELECT * FROM products
                    ${whereClause}
                    ORDER BY ${finalOrderBy} ${finalOrderDirection}
                `;

                console.log(`执行时组 ${group.name} 查询SQL:`, query);
                console.log('查询参数:', params);

                const results = await database.all(query, params);
                const allCandidates = results.results || [];
                console.log(`执行时组 ${group.name} 查询到 ${allCandidates.length} 个候选产品`);

                // 为每个产品添加组信息
                const candidatesWithGroupInfo = allCandidates.map(product => ({
                    ...product,
                    source_group_name: group.name,
                    source_group_index: group.originalIndex,
                    source_group_priority: group.priority,
                    source_group_random: group.positionConfig?.randomSelection || false
                }));

                groupCandidatesMap.set(group.originalIndex, candidatesWithGroupInfo);
            }

            // 第二步：简化的产品分配逻辑
            console.log('\n执行时第二步：开始产品分配');

            // 全局已选择的产品ID集合，确保不重复
            const globalSelectedIds = new Set();

            // 第三步：按优先级顺序为每个组分配产品

            for (const group of sortedGroups) {
                console.log(`\n执行时第三步：为组 ${group.name} (优先级: ${group.priority}) 分配产品`);

                const targetCount = parseInt(group.target) || 50;

                // 获取该组的候选产品
                let availableCandidates = groupCandidatesMap.get(group.originalIndex) || [];

                // 如果启用去重，过滤掉已被选择的产品
                if (comprehensiveConfig?.deduplicationEnabled) {
                    availableCandidates = availableCandidates.filter(product => !globalSelectedIds.has(product.product_id));
                }

                console.log(`执行时组 ${group.name} 可用候选产品: ${availableCandidates.length}`);

                let selectedProducts = [];

                // 如果启用随机选取
                if (group.positionConfig?.randomSelection && availableCandidates.length > targetCount) {
                    const shuffled = [...availableCandidates].sort(() => 0.5 - Math.random());
                    selectedProducts = shuffled.slice(0, targetCount);
                    console.log(`执行时组 ${group.name} 随机选取了 ${selectedProducts.length} 个产品`);
                } else {
                    selectedProducts = availableCandidates.slice(0, targetCount);
                    console.log(`执行时组 ${group.name} 按排序选取了 ${selectedProducts.length} 个产品`);
                }

                // 数量保证：如果数量不足且启用去重，从规则组1补充
                if (selectedProducts.length < targetCount && comprehensiveConfig?.deduplicationEnabled && group.priority !== 1) {
                    const shortfall = targetCount - selectedProducts.length;
                    console.log(`执行时组 ${group.name} 数量不足，需要补充 ${shortfall} 个产品`);

                    // 找到优先级1组
                    const priority1Group = sortedGroups.find(g => g.priority === 1);
                    if (priority1Group) {
                        let priority1Candidates = groupCandidatesMap.get(priority1Group.originalIndex) || [];

                        // 过滤掉已被选择的产品
                        priority1Candidates = priority1Candidates.filter(product =>
                            !globalSelectedIds.has(product.product_id) &&
                            !selectedProducts.some(selected => selected.product_id === product.product_id)
                        );

                        const supplementProducts = priority1Candidates.slice(0, shortfall);

                        // 为补充的产品标记并立即记录到全局已选择列表
                        supplementProducts.forEach(product => {
                            product.is_supplement = true;
                            globalSelectedIds.add(product.product_id);  // 立即记录，防止重复
                            console.log(`🔄 执行时补充产品 ${product.product_id} 从规则组1到组 ${group.name}`);
                        });

                        selectedProducts.push(...supplementProducts);
                        console.log(`执行时从优先级1组补充了 ${supplementProducts.length} 个产品到组 ${group.name}`);
                    }
                }

                // 记录已选择的产品ID（用于去重）
                selectedProducts.forEach(product => {
                    if (comprehensiveConfig?.deduplicationEnabled) {
                        if (globalSelectedIds.has(product.product_id)) {
                            console.log(`⚠️ 执行时警告：产品 ${product.product_id} 已经被选择过了！当前组: ${group.name}`);
                        }
                        globalSelectedIds.add(product.product_id);
                        console.log(`✅ 执行时记录产品 ${product.product_id} 到全局已选择列表，当前组: ${group.name}`);
                    }
                });

                console.log(`执行时组 ${group.name} 当前已选择产品数量: ${selectedProducts.length}`);
                console.log(`执行时全局已选择产品总数: ${globalSelectedIds.size}`);

                // 获取该组的实际位置范围
                const actualPositionRange = groupPositionMap.get(group.originalIndex);

                // 为每个产品添加最终的组信息和位置信息
                const productsWithPosition = selectedProducts.map((product, index) => ({
                    ...product,
                    group_name: group.name,
                    group_index: group.originalIndex,
                    priority: group.priority,
                    position_in_group: index + 1,
                    global_position: actualPositionRange.startPosition + index,
                    original_start_position: group.positionConfig?.startPosition || 1,
                    actual_start_position: actualPositionRange.startPosition
                }));

                // 保存组的产品信息，用于最终排序
                groupProductsMap.set(group.originalIndex, productsWithPosition);

                groupResults.push({
                    name: group.name,
                    target: targetCount,
                    selected: selectedProducts.length,
                    randomSelection: group.positionConfig?.randomSelection || false
                });
                totalSelected += selectedProducts.length;

                console.log(`执行时组 ${group.name} 最终选择了 ${selectedProducts.length} 个产品，位置范围: ${actualPositionRange.startPosition}-${actualPositionRange.startPosition + selectedProducts.length - 1}`);
            }

            // 合并所有产品并进行位置排序和跨组打乱
            let finalProductIds = [];
            
            // 收集所有产品
            let allProducts = [];
            for (const [groupIndex, products] of groupProductsMap) {
                allProducts = [...allProducts, ...products];
            }

            // 实现位置排序和跨组打乱功能
            if (comprehensiveConfig?.shuffleGroups && comprehensiveConfig.shuffleGroups.length >= 2) {
                console.log('执行时启用跨组打乱功能，打乱的组索引:', comprehensiveConfig.shuffleGroups);

                // 分离需要打乱的组和不需要打乱的组
                const shuffleGroupIndexes = new Set(comprehensiveConfig.shuffleGroups);
                const shuffleProducts = [];  // 需要打乱的产品
                const normalProducts = [];   // 不需要打乱的产品

                allProducts.forEach(product => {
                    if (shuffleGroupIndexes.has(product.group_index)) {
                        shuffleProducts.push(product);
                    } else {
                        normalProducts.push(product);
                    }
                });

                console.log(`执行时需要打乱的产品数量: ${shuffleProducts.length}`);
                console.log(`执行时不需要打乱的产品数量: ${normalProducts.length}`);

                // 对需要打乱的产品进行随机排序
                const shuffledProducts = [...shuffleProducts].sort(() => 0.5 - Math.random());

                // 重新分配位置：为打乱的产品重新分配连续的位置
                let shuffleStartPosition = Math.min(...shuffleProducts.map(p => p.global_position));
                shuffledProducts.forEach((product, index) => {
                    product.global_position = shuffleStartPosition + index;
                    product.is_shuffled = true;  // 标记为已打乱
                });

                // 重新合并产品列表
                allProducts = [...shuffledProducts, ...normalProducts];

                // 按最终位置排序
                allProducts.sort((a, b) => a.global_position - b.global_position);

                console.log('执行时跨组打乱完成，重新分配位置');
            } else {
                // 默认按位置排序
                allProducts.sort((a, b) => a.global_position - b.global_position);
                console.log('执行时按位置排序完成');
            }

            // 提取最终的产品ID列表
            finalProductIds = allProducts.map(product => product.product_id);

            // 更新产品批次号
            if (finalProductIds.length > 0) {
                const updateQuery = `
                    UPDATE products
                    SET batch_number = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE product_id IN (${finalProductIds.map(() => '?').join(',')})
                `;
                await database.run(updateQuery, [batchNumber, ...finalProductIds]);
                console.log(`执行时更新了 ${finalProductIds.length} 个产品的批次号: ${batchNumber}`);
            }

            // 保存执行记录
            const executionResult = {
                groups: groupResults,
                total_target,
                total_selected: totalSelected,
                shuffleGroups: comprehensiveConfig?.shuffleGroups || [],
                positionStrategy: comprehensiveConfig?.positionStrategy || 'position'
            };

            await database.run(`
                INSERT INTO selection_executions
                (rule_name, execution_time, total_selected, execution_result, batch_number, status)
                VALUES (?, ?, ?, ?, ?, ?)
            `, [
                rule_name,
                executionTime,
                totalSelected,
                JSON.stringify(executionResult),
                batchNumber,
                'completed'
            ]);

            await database.exec('COMMIT');

            console.log(`\n=== 执行完成 ===`);
            console.log(`总共选择了 ${totalSelected} 个产品`);
            console.log(`批次号: ${batchNumber}`);
            console.log(`是否启用跨组打乱: ${comprehensiveConfig?.shuffleGroups?.length >= 2 ? '是' : '否'}`);

            res.json({
                success: true,
                data: {
                    execution_time: executionTime,
                    total_selected: totalSelected,
                    batch_number: batchNumber,
                    group_results: groupResults,
                    shuffleGroups: comprehensiveConfig?.shuffleGroups || [],
                    positionStrategy: comprehensiveConfig?.positionStrategy || 'position'
                }
            });

        } catch (error) {
            await database.exec('ROLLBACK');
            throw error;
        }

    } catch (error) {
        console.error("Error executing selection rule:", error);
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
}

// 获取执行历史
async function handleGetExecutionHistory(req, res) {
  try {
    // 首先检查表是否存在，如果不存在则创建
    try {
      await database.exec(`
        CREATE TABLE IF NOT EXISTS selection_executions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          rule_name TEXT NOT NULL,
          execution_time TEXT NOT NULL,
          total_selected INTEGER DEFAULT 0,
          batch_number TEXT,
          status TEXT DEFAULT 'completed',
          error_message TEXT,
          execution_result TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
    } catch (createError) {
      console.error("Error creating selection_executions table:", createError);
    }

    const result = await database.all(`
      SELECT id, rule_name, execution_time, total_selected, batch_number, status, error_message
      FROM selection_executions
      ORDER BY execution_time DESC
      LIMIT 50
    `);

    res.json({
      success: true,
      data: result.results || []
    });
  } catch (error) {
    console.error("Error fetching execution history:", error);
    
    // 如果是表不存在的错误，返回空数组
    if (error.message && error.message.includes('no such table')) {
      res.json({
        success: true,
        data: []
      });
    } else {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }
}

// 获取执行详情
async function handleGetExecutionDetail(req, res) {
  try {
    const { executionId } = req.params;

    // 首先检查表是否存在
    try {
      await database.exec(`
        CREATE TABLE IF NOT EXISTS selection_executions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          rule_name TEXT NOT NULL,
          execution_time TEXT NOT NULL,
          total_selected INTEGER DEFAULT 0,
          batch_number TEXT,
          status TEXT DEFAULT 'completed',
          error_message TEXT,
          execution_result TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
    } catch (createError) {
      console.error("Error creating selection_executions table:", createError);
    }

    const result = await database.get(`
      SELECT * FROM selection_executions WHERE id = ?
    `, [executionId]);

    if (!result) {
      return res.status(404).json({
        success: false,
        message: "执行记录不存在"
      });
    }

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error("Error fetching execution detail:", error);
    
    // 如果是表不存在的错误
    if (error.message && error.message.includes('no such table')) {
      res.status(404).json({
        success: false,
        message: "执行记录不存在"
      });
    } else {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }
}

// 构建WHERE条件的辅助函数
function buildWhereClause(conditions) {
  if (!conditions || !Array.isArray(conditions) || conditions.length === 0) {
    return { whereClause: '', params: [] };
  }

  const whereConditions = [];
  const params = [];

  conditions.forEach(condition => {
    const { field, operator, value } = condition;

    if (!field || !operator || value === undefined || value === '') {
      return; // 跳过无效条件
    }

    // 验证字段名（防止SQL注入）
    const validFields = [
      'tag', 'commission_amount', 'product_price', 'sales_365_days',
      'sales_7_days_growth_rate', 'anchor_sales_30_days', 'product_source',
      'alliance_channel', 'product_type', 'anchor_name', 'shop_name'
    ];

    if (!validFields.includes(field)) {
      return; // 跳过无效字段
    }

    // 验证操作符（防止SQL注入）
    const validOperators = ['=', '>', '<', '>=', '<=', '!=', 'LIKE'];
    if (!validOperators.includes(operator)) {
      return; // 跳过无效操作符
    }

    // 构建条件
    if (operator === 'LIKE') {
      whereConditions.push(`${field} LIKE ?`);
      params.push(`%${value}%`);
    } else {
      whereConditions.push(`${field} ${operator} ?`);
      params.push(value);
    }
  });

  const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
  return { whereClause, params };
}

// 商品采集相关处理函数
const productCollection = new ProductCollection();

// 获取采集主播列表
async function handleGetCollectionAnchors(req, res) {
  try {
    const anchors = await productCollection.getAllActiveAnchors();
    res.json({
      success: true,
      data: anchors
    });
  } catch (error) {
    console.error('获取主播列表失败:', error);
    res.status(500).json({
      success: false,
      error: '获取主播列表失败: ' + error.message
    });
  }
}

// 商品采集处理
async function handleCollectProducts(req, res) {
  try {
    const { pageNum = 1, pageSize = 30, anchorId, tag, commissionRange, ...filters } = req.body;

    if (!anchorId) {
      return res.status(400).json({
        success: false,
        error: '请选择主播'
      });
    }

    if (!tag) {
      return res.status(400).json({
        success: false,
        error: '请选择商品标签'
      });
    }

    // 将佣金范围单独传递，不包含在filters中
    const filtersWithCommissionRange = commissionRange ? { ...filters, commissionRange } : filters;

    // 使用新的采集并保存方法，默认保存到数据库
    const result = await productCollection.collectAndSaveProducts(
      anchorId,
      filtersWithCommissionRange,  // 包含佣金范围的完整筛选条件
      pageNum,
      pageSize,
      true,  // 自动保存到数据库
      tag    // 传递tag参数
    );

    if (result.success) {
      // 构建响应数据
      const response = {
        success: true,
        data: result.data,
        currentPage: result.currentPage,
        end: result.end,
        total: result.total,
        originalCount: result.originalCount,  // 添加原始商品数量
        filteredCount: result.filteredCount,  // 添加筛选后商品数量
        anchorName: result.anchor.name
      };

      // 添加保存结果信息
      if (result.saveResult) {
        response.saveResult = {
          saved: result.saveResult.saved,
          total: result.saveResult.total,
          errors: result.saveResult.errors
        };

        console.log(`[${result.anchor.name}] 采集并保存商品: ${result.saveResult.saved}/${result.saveResult.total}`);
      }

      res.json(response);
    } else {
      console.error('商品采集失败，返回结果:', result);
      res.status(400).json(result);
    }

  } catch (error) {
    console.error('商品采集失败:', error);
    res.status(500).json({
      success: false,
      error: '商品采集失败: ' + error.message
    });
  }
}

/**
 * 处理批量更新主播Cookie请求
 */
async function handleBatchUpdateCookies(req, res) {
  try {
    console.log('=== 开始批量更新主播Cookie ===');

    const result = await batchUpdateAllAnchorsCookies();

    if (result.success) {
      console.log('=== 批量更新主播Cookie完成 ===');
      res.json({
        success: true,
        message: result.message,
        data: {
          totalAnchors: result.totalAnchors,
          updatedCount: result.updatedCount,
          noUpdateCount: result.noUpdateCount,
          failedCount: result.failedCount,
          results: result.results
        }
      });
    } else {
      console.error('=== 批量更新主播Cookie失败 ===');
      res.status(500).json({
        success: false,
        error: result.error,
        data: {
          totalAnchors: result.totalAnchors,
          updatedCount: result.updatedCount,
          noUpdateCount: result.noUpdateCount,
          failedCount: result.failedCount,
          results: result.results
        }
      });
    }
  } catch (error) {
    console.error('批量更新Cookie处理失败:', error);
    res.status(500).json({
      success: false,
      error: '批量更新失败: ' + error.message,
      data: {
        totalAnchors: 0,
        updatedCount: 0,
        noUpdateCount: 0,
        failedCount: 0,
        results: []
      }
    });
  }
}

/**
 * 处理获取Cookie更新状态请求
 */
async function handleGetCookieStatus(req, res) {
  try {
    const status = cookieScheduler.getStatus();
    const lastResult = status.stats.lastResult;

    res.json({
      success: true,
      data: {
        isRunning: status.isRunning,
        lastUpdateTime: status.lastUpdateTime,
        totalRuns: status.stats.totalRuns,
        successRuns: status.stats.successRuns,
        failedRuns: status.stats.failedRuns,
        lastResult: lastResult ? {
          totalAnchors: lastResult.totalAnchors,
          updatedCount: lastResult.updatedCount,
          noUpdateCount: lastResult.noUpdateCount,
          failedCount: lastResult.failedCount
        } : null
      }
    });
  } catch (error) {
    console.error('获取Cookie状态失败:', error);
    res.status(500).json({
      success: false,
      error: '获取Cookie状态失败: ' + error.message
    });
  }
}

export default router;